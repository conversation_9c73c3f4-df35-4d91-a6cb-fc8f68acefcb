<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\User;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Components\VisualPaginator\VisualPaginator;
use App\Event\Registered;
use App\Exceptions\UserException;
use App\FrontModule\Components\BoughtProducts\BoughtProducts;
use App\FrontModule\Components\BoughtProducts\BoughtProductsFactory;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordForm;
use App\FrontModule\Components\ChangePasswordForm\ChangePasswordFormFactory;
use App\FrontModule\Components\GoogleConnect\GoogleConnect;
use App\FrontModule\Components\GoogleConnect\GoogleConnectFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\FrontModule\Components\LostPasswordForm\LostPasswordForm;
use App\FrontModule\Components\LostPasswordForm\LostPasswordFormFactory;
use App\FrontModule\Components\MyLibrary\MyLibrary;
use App\FrontModule\Components\MyLibrary\MyLibraryFactory;
use App\FrontModule\Components\MyWatchdog\MyWatchdog;
use App\FrontModule\Components\MyWatchdog\MyWatchdogFactory;
use App\FrontModule\Components\OrderHistory\OrderHistory;
use App\FrontModule\Components\OrderHistory\OrderHistoryFactory;
use App\FrontModule\Components\ProfileForm\ProfileForm;
use App\FrontModule\Components\ProfileForm\ProfileFormFactory;
use App\FrontModule\Components\RegistrationForm\RegistrationForm;
use App\FrontModule\Components\RegistrationForm\RegistrationFormFactory;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\FrontModule\Components\SignInForm\SignInFormFactory;
use App\FrontModule\Components\UserAddressForm\UserAddressForm;
use App\FrontModule\Components\UserAddressForm\UserAddressFormFactory;
use App\FrontModule\Components\UserAnimalList\userAnimalList;
use App\FrontModule\Components\UserAnimalList\userAnimalListFactory;
use App\FrontModule\Components\UserAnimalForm\UserAnimalForm;
use App\FrontModule\Components\UserAnimalForm\UserAnimalFormFactory;
use App\FrontModule\Components\UserInterestForm\UserInterestForm;
use App\FrontModule\Components\UserInterestForm\UserInterestFormFactory;
use App\FrontModule\Components\UserSideMenu\UserSideMenu;
use App\FrontModule\Components\UserSideMenu\UserSideMenuFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\MyLibrary\MyLibraryRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use App\PostType\UserAnimal\Model\Orm\UserAnimal;
use App\Utils\Vocative\VocativeService;
use Nette\Application\AbortException;
use Nette\Application\Attributes\Persistent;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\UI\InvalidLinkException;
use Nette\DI\Attributes\Inject;
use Nette\Utils\Validators;
use Nette\Security\SimpleIdentity;
use Nette\Security\AuthenticationException;
use Exception;
use Throwable;
use App\Exceptions\LogicException;
use App\Model\Orm\User\User;
use Tracy\Debugger;
use Tracy\ILogger;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property Tree $object
 * @method Tree getObject()
 */
final class UserPresenter extends BasePresenter
{
	#[Persistent]
	public string $backlink = '';
	#[Persistent]
	public string $sharedLibraryUid = '';

	#[Persistent]
	public int $page = 1;

	#[Persistent]
	public int $boughtProductsPage = 1;

	#[Inject]
	public BoughtProductsFactory $boughtProductsFactory;

	private \App\Model\Orm\MyLibrary\MyLibrary|null $sharedLibrary = null;

	private userAnimal|null $userAnimal = null;

	public function __construct(
		private readonly ProfileFormFactory $profileFormFactory,
		private readonly ChangePasswordFormFactory $changePasswordFormFactory,
		private readonly LostPasswordFormFactory $lostPasswordFormFactory,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly RegistrationFormFactory $registrationFormFactory,
		private readonly UserSideMenuFactory $userSideMenuFactory,
		private readonly UserHashModel $userHashModel,
		private readonly GoogleConnectFactory $googleConnectFactory,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly UserAddressFormFactory $userAddressFormFactory,
		private readonly OrderHistoryFactory $orderHistoryFactory,
		private readonly MyLibraryFactory $myLibraryFactory,
		private readonly NewsletterEmailModel $newsletterEmailModel,
		private readonly MyLibraryRepository $myLibraryRepository,
		private readonly SignInFormFactory $signInFormFactory,
		private readonly MyWatchdogFactory $myWatchdogFactory,
		private readonly VocativeService $vocativeService,
		private readonly userAnimalListFactory $userAnimalListFactory,
		private readonly UserAnimalFormFactory $userAnimalFormFactory,
		private readonly UserInterestFormFactory $userInterestFormFactory

	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		if (isset($this->params['object'])) {
			$this->setObject($this->params['object']);
		}
	}

	public function actionSharedLibrary(string $sharedLibraryUid): void
	{
		$this->sharedLibrary = $this->myLibraryRepository->getBy(['uid' => $sharedLibraryUid]);
	}

	public function renderSharedLibrary(): void
	{
		if ($this->isAjax()) {
			if ($this->getParameter('more') !== null) {
				$this['sharedLibrary']['catalogProducts']->redrawControl('productList');
				$this['sharedLibrary']['catalogProducts']->redrawControl('productsPagerBottom');
			} else {
				if ( ! $this->isSignalRequest()) {
					$this['sharedLibrary']->redrawControl();
				}
			}
		}
	}

	public function renderLibrary(): void
	{
		if ($this->isAjax()) {
			if ($this->getParameter('more') !== null) {
				$this['myLibrary']['catalogProducts']->redrawControl('productList');
				$this['myLibrary']['catalogProducts']->redrawControl('productsPagerBottom');
			} else {
				if ( ! $this->isSignalRequest()) {
					$this['myLibrary']->redrawControl();
					$this['boughtProducts']->redrawControl();
				}
			}
		}
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();

		if (isset($this['canonicalUrl'])) {
			$this['canonicalUrl']->setForcedUrl($this->link('//this'));
		}

		if ( $this->getHttpRequest()->getCookie('registrationComplete') !== null ) {
			$this->eventDispatcher->dispatch(new Registered($this->userEntity));
		}
	}

	private function subscribeNewsletter(): void
	{
		if ($this->userEntity === null) {
			return;
		}

		if (!Validators::isEmail($this->userEntity->email)) {
			return;
		}

		try {
			$this->newsletterEmailModel->subscribeEmail($this->userEntity->email, $this->mutation);
		} catch (UserException) {

		}
		$this->orm->flush();
	}


	public function renderDefault(): void
	{
		$this->getTemplate()->isUserInClub = $this->userEntity->isClubMember;
		$this->getTemplate()->firstNameVocative = $this->userEntity->firstname ? $this->vocativeService->vocative($this->userEntity->firstname, $this->mutation, $this->userEntity->lastname) : null;
		$this->getTemplate()->hasNewsletterSubscription = $this->newsletterEmailModel->isSubscribedEmail($this->userEntity->email, $this->mutation);

		if ((bool)$this->getParameter("registrationComplete") && !empty($this->getMutation()?->pages->registrationOk->cf->reg_finish_ok->message)) {
			$this->getTemplate()->registrationCompleteMsg = $this->getMutation()?->pages->registrationOk->cf->reg_finish_ok->message;
		}
	}


	public function actionDefault(): void
	{
		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl();
			}
		} else {

			if ($this->getObject()->uid === Tree::UID_SHARED_LIBRARY) {
				return;
			}

			$this->redirectToLoginPage();
		}

		if ($this->getObject()->uid === Tree::UID_MY_REVIEWS) {
			$this->actionMyReviews();
		}

		$this->template->userEntity = $this->userEntity;
	}

	public function handleSubscribe(): void
	{
		$this->subscribeNewsletter();
		$this->redirect('this');
	}



	public function actionMyReviews(): void
	{
		$this->getTemplate()->productsToReview = $this->orm->product->findByIds($this->orm->productReview->getUnreviewProducts($this->userModel->findOrderedProductIds($this->user), $this->user));
		$this->template->reviews = $this->userEntity->reviews;
	}

	public function actionLogin(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}

	public function renderLogin(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl("userFormArea");
		}
	}


	public function actionLostPassword(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}

	public function renderLostPassword(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl("userFormArea");
		}
	}

	public function actionProfil(): void
	{
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}
	}


	public function actionRegistration(): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}
	}

	public function renderRegistration(): void
	{
		if ($this->isAjax()) {
			$this->redrawControl("userFormArea");
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {

			$userHash = $this->userHashModel->getHash($hashToken, UserHash::TYPE_LOST_PASSWORD);
			if ($userHash && $userHash->isValid()) {

			} else {

				$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
				$this->redirect($this->mutation->pages->lostPassword);
			}

		} else {
			$this->getComponent('lostPasswordForm')->flashMessage($this->translator->translate("reset_password_expired_link"), "error");
			$this->redirect($this->mutation->pages->lostPassword);
		}

	}

	public function actionRegistrationOk(?string $hashToken): void
	{
		if ($this->user->loggedIn) {
			$this->redirectToProfilePage();
		}

		// aktivace
		$this->template->isUserActivation = false;

		if ($hashToken) {
			$this->template->isUserActivation = true;
			$this->handleActivationRegistration($hashToken);
		}
	}

	/**
	 * @param string $hashToken
	 * @throws AbortException
	 */
	public function handleActivationRegistration(string $hashToken): void
	{
		try {
			$userHash = $this->userHashModel->getHash($hashToken, UserHash::HASH_TYPE_REGISTRATION);

			if (!isset($userHash)) {
				throw new Exception('Unknown UserHash');
			}

			if (!$userHash->isValid()) {
				throw new LogicException('user_is_already_active');
			}

			$user = $userHash->user;

			if (!$user) {
				throw new Exception('Unknown User');
			}

			$now = new DateTimeImmutable();
			$user->isActive = true;
			$user->activatedTime = $now;
			$user->lastLogin = $now;

			/** @var User $user */
			$user = $this->orm->user->persistAndFlush($user);

			$this->userHashModel->useHash($userHash);

			// force login
			$userIdentity = new SimpleIdentity($user->id, $user->role);
			$this->presenter->getUser()->login($userIdentity);
			$this->flashMessage('registration_activated_ok', 'ok');
			$this->redirectToUserPage('userAddress', ["newAddress" => 1, "registrationComplete" => 1]);

		} catch (LogicException $e) {
			$this->flashMessage($e->getMessage());

		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');

		} catch (AbortException $e) {
			throw $e;

		} catch (Throwable $e) {
			Debugger::log($e, ILogger::ERROR);
			$this->flashMessage('registration_activated_error', 'error');
		}
	}

	public function actionOrderHistory(?string $orderHash = null): void
	{
		if (isset($orderHash)) { // detail
			if ($this->user->loggedIn) {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
					'user' => $this->userEntity,
				]);
			} else {
				$order = $this->orm->order->getBy([
					'hash' => $orderHash,
				]);
			}
			$this->template->order = $order;
		} elseif (!$this->user->loggedIn) { // list
			$this->redirectToLoginPage();
		}

		if ($this->user->loggedIn) {
			if ($this->isAjax()) {
				$this->redrawControl('userOrderHistoryArea');
				$this->redrawControl('orderHistoryControl');
			}
		}
	}

	/**
	 * Adds all purchasable items from a specific order to the cart
	 *
	 * @param int|null $orderId Order ID (optional if hash is provided)
	 * @param string|null $orderHash Order hash (optional if orderId is provided)
	 */
	public function handleRepeatOrder(?int $orderId = null, ?string $orderHash = null): void
	{
		// Find the order by ID or hash
		$order = null;

		if ($orderId !== null) {
			$order = $this->orm->order->getById($orderId);
		} elseif ($orderHash !== null) {
			$order = $this->orm->order->getBy(['hash' => $orderHash]);
		}

		if ($order === null) {
			$this->flashMessage('order_not_found', 'error');
			if (!$this->isAjax()) {
				$this->redirect($this->mutation->pages->cart);
			}
		}

		// Check if the order belongs to the current user
		if ($this->userEntity === null || $order->user->id === $this->userEntity->id) {
			// Get all purchasable items from the order
			$buyableItemsNoLimits = $order->getBuyableItems();
			$buyableItems = $order->getBuyableItems(null, true, true);
			$copyNotCompleted = count($buyableItemsNoLimits) > count($buyableItems);
			$copyItemNotFull = false;

			// Add each item to the shopping cart
			foreach ($buyableItems as $item) {
				if ($item->variant->getterProductAvailability()->getMaxAvailableAmount() < $item->amount) {
					$copyItemNotFull = true;
				}
				if ($item instanceof \App\Model\Orm\Order\Product\ProductItem && $item->variant !== null) {
					// Add product variant to cart
					$this->shoppingCart->addProduct($item->variant, $item->amount);
				} elseif ($item instanceof \App\Model\Orm\Order\Class\ClassItem && $item->product !== null) {
					// Add class to cart
					$this->shoppingCart->addClass($item->product, $item->classEvent, $item->priceLevel, $item->amount);
				}
			}
			if ($copyNotCompleted || $copyItemNotFull) {
				if ($copyNotCompleted) {
					$this->flashMessage($this->translator->translate('some_items_not_added_to_cart'));
				}
				if ($copyItemNotFull) {
					$this->flashMessage($this->translator->translate('some_items_not_added_to_cart_in_full_amount'));
				}
			} else {
				$this->flashMessage($this->translator->translate('items_added_to_cart'), 'ok');
			}
		}

		$this->redirect($this->mutation->pages->cart);
	}

	public function handleLogout(): void
	{
		$this->getUser()->logout(TRUE);
		$this->flashMessage('msg_info_logout');
		$this->redirectToUserPage('title');
	}


	public function createComponentVp(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}


	/**
	 * presmerovani na prihlasovaci stranku, v pripade pristupu do zabezpecene sekce
	 */
	private function redirectToLoginPage(): never
	{
		$this->redirectToUserPage('userLogin');
	}


	/**
	 * presmerovani na detail uziv. sekce, pri pristupu na registraci, zapomenute heslo a prihlaseni
	 */
	private function redirectToProfilePage(): never
	{
		$this->redirectToUserPage('userSection');
	}


	/**
	 * @param string $uid
	 * @throws AbortException
	 * @throws InvalidLinkException
	 */
	private function redirectToUserPage(string $uid, array $params = []): never
	{
		// kdyz je ajax a fancybox=true - JS presmerovani
		if ($this->isAjax()) {
			if (isset($_GET['fancybox'])) {
//				$this->presenter->setLayout(false);
//				$this->setView("extra");
				$url = $this->link($this->mutation->pages->$uid);
				$urlParams = http_build_query($params);
				if ($urlParams) {
					$url .= '?' . $urlParams;
				}
//				echo '<meta http-equiv="refresh" content="0; url='.$url.'" />';
				echo '<script>
                            window.location = "' . $url . '"
						</script>';

				$this->terminate();
			}
		}

		//, array('backlink' => $this->storeRequest())
		$this->redirect($this->mutation->pages->$uid, $params);
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}


	protected function createComponentUserSideMenu(): UserSideMenu
	{
		return $this->userSideMenuFactory->create($this->object, $this->userEntity);
	}


	public function createComponentRegistrationForm(): RegistrationForm
	{
		return $this->registrationFormFactory->create($this->object);
	}


	public function createComponentProfileForm(): ProfileForm
	{
		return $this->profileFormFactory->create($this->object, $this->userEntity);
	}


	public function createComponentChangePasswordForm(): ChangePasswordForm
	{
		return $this->changePasswordFormFactory->create($this->object, $this->userEntity);
	}

	public function createComponentLostPasswordForm(): LostPasswordForm
	{
		$hash = NULL;
		$params = $this->getParameters();
		if (isset($params['hashToken'])) {
			$hash = $params['hashToken'];
		}
		return $this->lostPasswordFormFactory->create($this->mutation, $this->object, $hash);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		return $this->googleLoginFactory->create($this->mutation);
	}

	protected function createComponentGoogleConnect(): GoogleConnect
	{
		return $this->googleConnectFactory->create($this->mutation);
	}

	public function createComponentUserAddressForm(): UserAddressForm
	{
		return $this->userAddressFormFactory->create($this->getObject(), $this->userEntity);
	}

	public function createComponentOrderHistory(): OrderHistory
	{
		return $this->orderHistoryFactory->create($this->getObject(), $this->userEntity, $this->mutation);
	}

	public function createComponentSharedLibrary(): MyLibrary
	{
		return $this->myLibraryFactory->create($this->getObject(), $this->sharedLibrary, $this->setup, $this->getPageParameter());
	}
	public function createComponentMyLibrary(): MyLibrary
	{

		return $this->myLibraryFactory->create($this->getObject(), $this->userEntity?->library, $this->setup, $this->getPageParameter());
	}

	public function createComponentBoughtProducts(): BoughtProducts
	{
		return $this->boughtProductsFactory->create(
			$this->getObject(),
			$this->setup,
			$this->boughtProductsPage,
		);
	}

	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	public function createComponentMyWatchdog(): MyWatchdog
	{
		return $this->myWatchdogFactory->create($this->setup/*, $this->getPageParameter()*/);
	}

	public function createComponentUserAnimalList(): userAnimalList
	{
		return $this->userAnimalListFactory->create($this->getObject(), $this->userEntity, $this->mutation);
	}

	public function actionAnimalEdit(int|null $id = null): void {
		if (!$this->user->loggedIn) {
			$this->redirectToLoginPage();
		}

		$this->template->userAnimal = null;

		if ($id) {
			$this->userAnimal = $this->userEntity->animals->toCollection()->getById($id);

			if (!$this->userAnimal) {
				throw new ForbiddenRequestException(sprintf('Unknown Animal ID %s', $id));
			}

			if ($this->userAnimal->user->id != $this->presenter->getUser()->id) {
				throw new ForbiddenRequestException(sprintf('Editing Animal ID %s is not allowed for user ID %s', $this->userAnimal->user->id, $this->presenter->getUser()->id));
			}
		}

		$this->template->userAnimal = $this->userAnimal;
	}

	public function createComponentUserAnimalForm(): UserAnimalForm
	{
		return $this->userAnimalFormFactory->create($this->mutation, $this->userAnimal);
	}

	public function createComponentUserInterestForm(): UserInterestForm
	{
		return $this->userInterestFormFactory->create($this->object, $this->userEntity);
	}

}
